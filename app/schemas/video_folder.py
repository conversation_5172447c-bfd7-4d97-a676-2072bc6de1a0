from datetime import datetime

from pydantic import BaseModel, Field


class VideoFolderBase(BaseModel):
    """视频文件夹基础模型"""

    name: str = Field(..., description="文件夹名称")
    path: str = Field(..., description="文件夹路径，以/开头，如/favorites")


class VideoFolderCreate(VideoFolderBase):
    """创建视频文件夹请求模型"""

    pass


class VideoFolderUpdate(BaseModel):
    """更新视频文件夹请求模型"""

    name: str | None = Field(None, description="文件夹名称")


class VideoFolderInDB(VideoFolderBase):
    """数据库中的视频文件夹模型"""

    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class VideoFolder(VideoFolderInDB):
    """API响应的视频文件夹模型"""

    video_count: int | None = Field(0, description="文件夹中的视频数量")
    has_children: bool | None = Field(False, description="是否有子文件夹")


class VideoFolderTree(VideoFolder):
    """视频文件夹树形结构模型"""

    children: list["VideoFolderTree"] = Field(default_factory=list, description="子文件夹列表")
