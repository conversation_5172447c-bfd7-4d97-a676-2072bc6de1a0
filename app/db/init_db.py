import logging

from sqlalchemy.ext.asyncio import AsyncSession

from app.db.init_permissions import init_permissions
from app.db.session import Base, engine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def init_db(db: AsyncSession) -> None:
    """初始化数据库"""
    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("数据库表已创建")

    # 执行点赞和收藏表迁移
    try:
        from app.db.migrate_like_favorite import migrate_like_favorite_tables

        migrate_like_favorite_tables()
        logger.info("点赞和收藏表迁移完成")
    except Exception as e:
        logger.error(f"点赞和收藏表迁移失败: {e}")

    # 初始化权限和角色
    init_permissions(db)
