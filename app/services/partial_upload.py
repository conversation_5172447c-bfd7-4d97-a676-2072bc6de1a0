"""分片上传文件到阿里云oss"""

import hashlib

import oss2
from oss2 import determine_part_size
from oss2.credentials import EnvironmentVariableCredentialsProvider
from oss2.models import PartInfo
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from app.core.config import get_settings
from app.services.logger import get_logger

settings = get_settings()
logger = get_logger(__name__)


class SendOssError(Exception):
    pass


class PartialUpload:
    """
    分片上传文件到阿里云oss
    使用分片上传和断点续传
    文件上传错误重试
    1. 计算当前文件的哈希值
        (1). 如果文件已经存在， 则直接返回oss文件地址
        (2). 如果文件不存在， 则计算哈希值 使用异步队列将图像转为webp格式
    """

    def __init__(self):
        self.oss_client = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())

    async def calculate_file_hash(self, file) -> str:
        """计算文件哈希值（流式处理）"""
        file_size = 0
        hash_algorithm = hashlib.sha256()
        await file.seek(0)  # 确保从文件开头开始
        while chunk := await file.read(1024 * 1024):  # 每次读取1MB
            hash_algorithm.update(chunk)
            file_size += len(chunk)
        # 重置文件指针
        await file.seek(0)
        return hash_algorithm.hexdigest()

    def bucket_client(self):
        return oss2.Bucket(
            self.oss_client,
            settings.OSS_ENDPOINT,
            settings.OSS_BUCKET_NAME,
            region=settings.OSS_REGION,
        )

    @retry(
        stop=stop_after_attempt(3),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=4, max=10),  # 指数退避重试间隔
        retry=retry_if_exception_type(
            (oss2.exceptions.OssError, SendOssError)
        ),  # 指定需要重试的异常类型
        reraise=True,  # 重试失败后抛出原始异常
    )
    def upload(self, webp_data: bytes, file_hash: str):
        bucket = self.bucket_client()
        oss_path = f"steam/images/{file_hash}.webp"  # 使用字符串拼接即可
        file_size = len(webp_data)

        # 小文件直接上传
        if file_size < 100 * 1024 * 1024:
            try:
                result = bucket.put_object(oss_path, webp_data)
                return result
            except Exception as e:
                raise SendOssError(f"Upload failed: {str(e)}") from e

        # 大文件分片上传（手动分片）
        try:
            # 确定分片大小
            part_size = determine_part_size(file_size, preferred_size=1 * 1024 * 1024)
            # 初始化分片上传
            upload_id = bucket.init_multipart_upload(oss_path).upload_id
            parts = []  # 用于保存已上传的分片信息

            # 分片上传
            part_number = 1
            offset = 0
            while offset < file_size:
                # 计算当前分片大小
                num_to_upload = min(part_size, file_size - offset)
                # 获取当前分片的数据
                part_data = webp_data[offset : offset + num_to_upload]
                # 上传分片
                result = bucket.upload_part(oss_path, upload_id, part_number, part_data)
                # 保存分片信息（包含分片号PartNumber和ETag）
                parts.append(PartInfo(part_number, result.etag))
                # 更新offset
                offset += num_to_upload
                part_number += 1

            # 完成分片上传
            result = bucket.complete_multipart_upload(oss_path, upload_id, parts)
            return result

        except Exception as e:
            # 上传失败，中止上传
            try:
                bucket.abort_multipart_upload(oss_path, upload_id)
            except Exception:
                logger.warning("Abort multipart upload failed", exc_info=True)
            raise SendOssError(f"Upload failed: {str(e)}") from e
