"""推荐缓存服务模块"""

import json
import logging
from datetime import datetime
from typing import Any

from app.db.redis import get_redis
from app.schemas.recommendation import RecommendationItem, RecommendationResponse

logger = logging.getLogger(__name__)


class RecommendationCacheService:
    """推荐缓存服务类"""

    def __init__(self):
        self.redis_client = None

        # 缓存键前缀
        self.user_recommendations_prefix = "user_recommendations"
        self.hot_content_prefix = "hot_content"
        self.similar_content_prefix = "similar_content"
        self.user_profile_prefix = "user_profile"
        self.content_features_prefix = "content_features"

        # 缓存过期时间（秒）
        self.user_recommendations_expire = 1800  # 30分钟
        self.hot_content_expire = 3600  # 1小时
        self.similar_content_expire = 7200  # 2小时
        self.user_profile_expire = 86400  # 24小时
        self.content_features_expire = 86400  # 24小时

    async def _ensure_redis_client(self) -> None:
        """确保Redis客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    def get_user_recommendations(
        self,
        user_id: int,
        algorithm_type: str,
        content_type: str | None = None,
        position: str | None = None,
    ) -> RecommendationResponse | None:
        """获取用户推荐缓存"""
        try:
            key = self._get_user_recommendations_key(
                user_id, algorithm_type, content_type, position
            )
            cached_data = self.redis_client.get(key)

            if cached_data:
                data = json.loads(cached_data.decode("utf-8"))

                # 转换为RecommendationResponse对象
                items = [
                    RecommendationItem(
                        content_type=item["content_type"],
                        content_id=item["content_id"],
                        score=item["score"],
                        reason=item.get("reason"),
                    )
                    for item in data["items"]
                ]

                return RecommendationResponse(
                    items=items,
                    algorithm_type=data["algorithm_type"],
                    total_count=data["total_count"],
                    generated_at=datetime.fromisoformat(data["generated_at"]),
                )

            return None
        except Exception as e:
            logger.error(f"获取用户推荐缓存失败: {e}")
            return None

    def set_user_recommendations(
        self,
        user_id: int,
        algorithm_type: str,
        recommendations: RecommendationResponse,
        content_type: str | None = None,
        position: str | None = None,
    ) -> bool:
        """设置用户推荐缓存"""
        try:
            key = self._get_user_recommendations_key(
                user_id, algorithm_type, content_type, position
            )

            # 转换为可序列化的格式
            data = {
                "items": [
                    {
                        "content_type": item.content_type,
                        "content_id": item.content_id,
                        "score": item.score,
                        "reason": item.reason,
                    }
                    for item in recommendations.items
                ],
                "algorithm_type": recommendations.algorithm_type,
                "total_count": recommendations.total_count,
                "generated_at": recommendations.generated_at.isoformat(),
            }

            cached_data = json.dumps(data, ensure_ascii=False)
            self.redis_client.setex(key, self.user_recommendations_expire, cached_data)

            return True
        except Exception as e:
            logger.error(f"设置用户推荐缓存失败: {e}")
            return False

    def get_hot_content(
        self, content_type: str, time_range: str = "week"
    ) -> list[dict[str, Any]] | None:
        """获取热门内容缓存"""
        try:
            key = self._get_hot_content_key(content_type, time_range)
            cached_data = self.redis_client.get(key)

            if cached_data:
                return json.loads(cached_data.decode("utf-8"))

            return None
        except Exception as e:
            logger.error(f"获取热门内容缓存失败: {e}")
            return None

    def set_hot_content(
        self, content_type: str, hot_content: list[dict[str, Any]], time_range: str = "week"
    ) -> bool:
        """设置热门内容缓存"""
        try:
            key = self._get_hot_content_key(content_type, time_range)
            cached_data = json.dumps(hot_content, ensure_ascii=False)
            self.redis_client.setex(key, self.hot_content_expire, cached_data)

            return True
        except Exception as e:
            logger.error(f"设置热门内容缓存失败: {e}")
            return False

    def get_similar_content(
        self, content_type: str, content_id: int, similarity_type: str = "hybrid"
    ) -> list[RecommendationItem] | None:
        """获取相似内容缓存"""
        try:
            key = self._get_similar_content_key(content_type, content_id, similarity_type)
            cached_data = self.redis_client.get(key)

            if cached_data:
                data = json.loads(cached_data.decode("utf-8"))
                return [
                    RecommendationItem(
                        content_type=item["content_type"],
                        content_id=item["content_id"],
                        score=item["score"],
                        reason=item.get("reason"),
                    )
                    for item in data
                ]

            return None
        except Exception as e:
            logger.error(f"获取相似内容缓存失败: {e}")
            return None

    def set_similar_content(
        self,
        content_type: str,
        content_id: int,
        similar_items: list[RecommendationItem],
        similarity_type: str = "hybrid",
    ) -> bool:
        """设置相似内容缓存"""
        try:
            key = self._get_similar_content_key(content_type, content_id, similarity_type)

            data = [
                {
                    "content_type": item.content_type,
                    "content_id": item.content_id,
                    "score": item.score,
                    "reason": item.reason,
                }
                for item in similar_items
            ]

            cached_data = json.dumps(data, ensure_ascii=False)
            self.redis_client.setex(key, self.similar_content_expire, cached_data)

            return True
        except Exception as e:
            logger.error(f"设置相似内容缓存失败: {e}")
            return False

    def get_user_profile_cache(self, user_id: int) -> dict[str, Any] | None:
        """获取用户画像缓存"""
        try:
            key = self._get_user_profile_key(user_id)
            cached_data = self.redis_client.get(key)

            if cached_data:
                return json.loads(cached_data.decode("utf-8"))

            return None
        except Exception as e:
            logger.error(f"获取用户画像缓存失败: {e}")
            return None

    def set_user_profile_cache(self, user_id: int, profile_data: dict[str, Any]) -> bool:
        """设置用户画像缓存"""
        try:
            key = self._get_user_profile_key(user_id)
            cached_data = json.dumps(profile_data, ensure_ascii=False)
            self.redis_client.setex(key, self.user_profile_expire, cached_data)

            return True
        except Exception as e:
            logger.error(f"设置用户画像缓存失败: {e}")
            return False

    def get_content_features(self, content_type: str, content_id: int) -> dict[str, Any] | None:
        """获取内容特征缓存"""
        try:
            key = self._get_content_features_key(content_type, content_id)
            cached_data = self.redis_client.get(key)

            if cached_data:
                return json.loads(cached_data.decode("utf-8"))

            return None
        except Exception as e:
            logger.error(f"获取内容特征缓存失败: {e}")
            return None

    def set_content_features(
        self, content_type: str, content_id: int, features: dict[str, Any]
    ) -> bool:
        """设置内容特征缓存"""
        try:
            key = self._get_content_features_key(content_type, content_id)
            cached_data = json.dumps(features, ensure_ascii=False)
            self.redis_client.setex(key, self.content_features_expire, cached_data)

            return True
        except Exception as e:
            logger.error(f"设置内容特征缓存失败: {e}")
            return False

    def invalidate_user_cache(self, user_id: int) -> bool:
        """清除用户相关的所有缓存"""
        try:
            # 清除用户推荐缓存
            pattern = f"{self.user_recommendations_prefix}:{user_id}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)

            # 清除用户画像缓存
            profile_key = self._get_user_profile_key(user_id)
            self.redis_client.delete(profile_key)

            return True
        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")
            return False

    def invalidate_content_cache(self, content_type: str, content_id: int) -> bool:
        """清除内容相关的所有缓存"""
        try:
            # 清除相似内容缓存
            pattern = f"{self.similar_content_prefix}:{content_type}:{content_id}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)

            # 清除内容特征缓存
            features_key = self._get_content_features_key(content_type, content_id)
            self.redis_client.delete(features_key)

            # 清除热门内容缓存（因为可能影响排序）
            hot_pattern = f"{self.hot_content_prefix}:{content_type}:*"
            hot_keys = self.redis_client.keys(hot_pattern)
            if hot_keys:
                self.redis_client.delete(*hot_keys)

            return True
        except Exception as e:
            logger.error(f"清除内容缓存失败: {e}")
            return False

    def _get_user_recommendations_key(
        self,
        user_id: int,
        algorithm_type: str,
        content_type: str | None = None,
        position: str | None = None,
    ) -> str:
        """获取用户推荐缓存键"""
        key_parts = [self.user_recommendations_prefix, str(user_id), algorithm_type]

        if content_type:
            key_parts.append(content_type)
        else:
            key_parts.append("all")

        if position:
            key_parts.append(position)
        else:
            key_parts.append("default")

        return ":".join(key_parts)

    def _get_hot_content_key(self, content_type: str, time_range: str) -> str:
        """获取热门内容缓存键"""
        return f"{self.hot_content_prefix}:{content_type}:{time_range}"

    def _get_similar_content_key(
        self, content_type: str, content_id: int, similarity_type: str
    ) -> str:
        """获取相似内容缓存键"""
        return f"{self.similar_content_prefix}:{content_type}:{content_id}:{similarity_type}"

    def _get_user_profile_key(self, user_id: int) -> str:
        """获取用户画像缓存键"""
        return f"{self.user_profile_prefix}:{user_id}"

    def _get_content_features_key(self, content_type: str, content_id: int) -> str:
        """获取内容特征缓存键"""
        return f"{self.content_features_prefix}:{content_type}:{content_id}"


# 创建全局推荐缓存服务实例
recommendation_cache_service = RecommendationCacheService()
