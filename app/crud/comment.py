from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.comment import Comment, CommentType
from app.schemas.comment import CommentCreate, CommentUpdate


class CRUDComment(CRUDBase[Comment, CommentCreate, CommentUpdate]):
    async def get_by_article(
        self, db: AsyncSession, *, article_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取文章的评论列表"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_video(
        self, db: AsyncSession, *, video_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取视频的评论列表"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_user(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取用户的评论列表"""
        query = (
            select(self.model)
            .filter(self.model.author_id == author_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_article(self, db: AsyncSession, *, article_id: int) -> int:
        """统计文章的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()

    async def count_by_video(self, db: AsyncSession, *, video_id: int) -> int:
        """统计视频的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()


comment = CRUDComment(Comment)
