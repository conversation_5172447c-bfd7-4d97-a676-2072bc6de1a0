from datetime import datetime

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.favorite import Favorite
from app.schemas.favorite import FavoriteCreate, FavoriteUpdate


class CRUDFavorite(CRUDBase[Favorite, FavoriteCreate, FavoriteUpdate]):
    """收藏CRUD操作"""

    async def get_by_user_and_content(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> Favorite | None:
        """根据用户和内容获取收藏记录"""
        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
        )
        return result.scalar_one_or_none()

    async def toggle_favorite(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str,
        content_id: int,
        note: str | None = None,
    ) -> tuple[Favorite, bool]:
        """切换收藏状态"""
        existing_favorite = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )

        if existing_favorite:
            existing_favorite.is_active = not existing_favorite.is_active
            existing_favorite.updated_at = datetime.utcnow()
            if note is not None:
                existing_favorite.note = note
            db.add(existing_favorite)
            await db.commit()
            await db.refresh(existing_favorite)
            return existing_favorite, existing_favorite.is_active
        else:
            favorite_data = {
                "user_id": user_id,
                "content_type": content_type,
                "content_id": content_id,
                "note": note,
                "is_active": True,
            }
            new_favorite = self.model(**favorite_data)
            db.add(new_favorite)
            await db.commit()
            await db.refresh(new_favorite)
            return new_favorite, True


favorite = CRUDFavorite(Favorite)
