from typing import Any

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.get("/likes", response_model=schemas.LikeHistory)
async def get_user_like_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选: article, video"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户点赞历史记录"""
    likes = await crud.like.get_user_likes(
        db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
    )
    total = await crud.like.get_user_like_count(db, user_id=current_user.id, content_type=content_type)

    return schemas.LikeHistory(total=total, items=likes)


@router.get("/favorites", response_model=schemas.FavoriteHistory)
async def get_user_favorite_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选: article, video"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户收藏历史记录"""
    favorites = await crud.favorite.get_user_favorites(
        db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
    )
    total = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type=content_type
    )

    return schemas.FavoriteHistory(total=total, items=favorites)


@router.get("/favorites/with-content", response_model=list[schemas.FavoriteWithContent])
async def get_user_favorite_history_with_content(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选: article, video"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户收藏历史记录（包含内容详情）"""
    favorites = await crud.favorite.get_user_favorites(
        db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
    )

    # 获取内容详情
    favorites_with_content = []
    for favorite in favorites:
        favorite_dict = favorite.__dict__.copy()

        # 根据内容类型获取内容详情
        if favorite.content_type == "article":
            content = await crud.article.get(db, id=favorite.content_id)
            if content:
                favorite_dict.update(
                    {
                        "content_title": content.title,
                        "content_author": content.author.username if content.author else None,
                        "content_created_at": content.created_at,
                    }
                )
        elif favorite.content_type == "video":
            content = await crud.video.get(db, id=favorite.content_id)
            if content:
                favorite_dict.update(
                    {
                        "content_title": content.title,
                        "content_author": content.author.username if content.author else None,
                        "content_created_at": content.created_at,
                    }
                )

        favorites_with_content.append(schemas.FavoriteWithContent(**favorite_dict))

    return favorites_with_content


@router.get("/stats", response_model=dict)
async def get_user_activity_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户活动统计"""
    # 获取点赞统计
    total_likes = await crud.like.get_user_like_count(db, user_id=current_user.id)
    article_likes = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_likes = await crud.like.get_user_like_count(db, user_id=current_user.id, content_type="video")

    # 获取收藏统计
    total_favorites = await crud.favorite.get_user_favorite_count(db, user_id=current_user.id)
    article_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="video"
    )

    return {
        "likes": {
            "total": total_likes,
            "articles": article_likes,
            "videos": video_likes,
        },
        "favorites": {
            "total": total_favorites,
            "articles": article_favorites,
            "videos": video_favorites,
        },
    }


@router.get("/recent-activity", response_model=dict)
async def get_user_recent_activity(
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(10, ge=1, le=50, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户最近活动"""
    # 获取最近的点赞
    recent_likes = await crud.like.get_user_likes(db, user_id=current_user.id, skip=0, limit=limit)

    # 获取最近的收藏
    recent_favorites = await crud.favorite.get_user_favorites(
        db, user_id=current_user.id, skip=0, limit=limit
    )

    return {
        "recent_likes": [
            {
                "id": like.id,
                "content_type": like.content_type,
                "content_id": like.content_id,
                "created_at": like.created_at,
            }
            for like in recent_likes
        ],
        "recent_favorites": [
            {
                "id": favorite.id,
                "content_type": favorite.content_type,
                "content_id": favorite.content_id,
                "note": favorite.note,
                "created_at": favorite.created_at,
            }
            for favorite in recent_favorites
        ],
    }
