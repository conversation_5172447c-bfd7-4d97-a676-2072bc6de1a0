"""422错误处理演示API"""

from enum import Enum

from fastapi import APIRouter, Body, HTTPException, Query
from pydantic import BaseModel, EmailStr, Field, validator

router = APIRouter()


class UserRole(str, Enum):
    """用户角色枚举"""

    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"


class UserCreateDemo(BaseModel):
    """用户创建演示模型"""

    username: str = Field(
        ..., min_length=3, max_length=20, description="用户名，3-20个字符"
    )
    email: EmailStr = Field(..., description="邮箱地址")
    age: int = Field(..., ge=18, le=120, description="年龄，18-120岁")
    role: UserRole = Field(default=UserRole.USER, description="用户角色")
    tags: list[str] = Field(default=[], max_items=5, description="标签，最多5个")
    bio: str | None = Field(None, max_length=500, description="个人简介，最多500字符")
    is_active: bool = Field(default=True, description="是否激活")

    @validator("username")
    def validate_username(cls, v):
        """验证用户名"""
        if not v.isalnum():
            raise ValueError("用户名只能包含字母和数字")
        return v

    @validator("tags")
    def validate_tags(cls, v):
        """验证标签"""
        if len(v) != len(set(v)):
            raise ValueError("标签不能重复")
        return v


class ProductDemo(BaseModel):
    """产品演示模型"""

    name: str = Field(..., min_length=1, max_length=100)
    price: float = Field(..., gt=0, description="价格必须大于0")
    category_id: int = Field(..., ge=1, description="分类ID必须大于等于1")
    description: str | None = Field(None, max_length=1000)
    in_stock: bool = Field(default=True)

    class Config:
        json_schema_extra = {
            "example": {
                "name": "示例产品",
                "price": 99.99,
                "category_id": 1,
                "description": "这是一个示例产品",
                "in_stock": True,
            }
        }


@router.post("/users/demo", summary="用户创建演示", description="演示各种422验证错误")
async def create_user_demo(user: UserCreateDemo):
    """
    创建用户演示 - 会触发各种422错误

    常见的422错误场景：
    1. 缺少必需字段
    2. 字段类型错误
    3. 字段值不符合验证规则
    4. 枚举值错误
    5. 自定义验证器失败
    """
    return {"message": "用户创建成功", "user": user.dict()}


@router.post("/products/demo")
async def create_product_demo(product: ProductDemo):
    """
    产品创建演示

    测试用例：
    - price 必须大于0
    - category_id 必须大于等于1
    - name 不能为空
    """
    return {"message": "产品创建成功", "product": product.dict()}


@router.get("/search/demo")
async def search_demo(
    q: str = Query(..., min_length=1, max_length=100, description="搜索关键词"),
    page: int = Query(1, ge=1, le=1000, description="页码，1-1000"),
    size: int = Query(10, ge=1, le=100, description="每页大小，1-100"),
    sort_by: str | None = Query(
        None, regex="^(name|price|created_at)$", description="排序字段"
    ),
    order: str | None = Query("asc", regex="^(asc|desc)$", description="排序方向"),
):
    """
    搜索演示 - Query参数验证

    测试用例：
    - q 不能为空
    - page 必须在1-1000之间
    - size 必须在1-100之间
    - sort_by 只能是 name, price, created_at
    - order 只能是 asc, desc
    """
    return {
        "query": q,
        "page": page,
        "size": size,
        "sort_by": sort_by,
        "order": order,
        "results": [],
    }


@router.post("/complex/demo")
async def complex_validation_demo(
    data: dict = Body(
        ...,
        example={
            "user": {"username": "testuser", "email": "<EMAIL>", "age": 25},
            "products": [
                {"name": "产品1", "price": 99.99},
                {"name": "产品2", "price": 199.99},
            ],
            "metadata": {"source": "api", "timestamp": "2023-01-01T00:00:00Z"},
        },
    ),
):
    """
    复杂数据验证演示

    这个端点接受复杂的嵌套数据结构，
    可以测试嵌套对象的验证错误
    """
    # 手动验证示例
    if "user" not in data:
        raise HTTPException(status_code=422, detail="缺少user字段")

    user = data["user"]
    if not isinstance(user.get("age"), int) or user["age"] < 18:
        raise HTTPException(status_code=422, detail="用户年龄必须是18岁以上的整数")

    return {"message": "复杂数据验证成功", "data": data}


@router.post("/file-upload/demo")
async def file_upload_demo(
    file_type: str = Query(..., regex="^(image|document|video)$"),
    max_size: int = Query(..., ge=1, le=100, description="最大文件大小(MB)"),
    allowed_extensions: list[str] = Query(..., description="允许的文件扩展名"),
):
    """
    文件上传参数验证演示

    测试用例：
    - file_type 只能是 image, document, video
    - max_size 必须在1-100之间
    - allowed_extensions 必须是列表
    """
    return {
        "file_type": file_type,
        "max_size": max_size,
        "allowed_extensions": allowed_extensions,
    }


# 自定义验证函数示例
def validate_phone_number(phone: str) -> str:
    """验证手机号码"""
    import re

    pattern = r"^1[3-9]\d{9}$"
    if not re.match(pattern, phone):
        raise ValueError("手机号码格式不正确")
    return phone


@router.post("/phone/demo")
async def phone_validation_demo(phone: str = Body(..., description="手机号码")):
    """
    手机号码验证演示
    """
    try:
        validated_phone = validate_phone_number(phone)
        return {"message": "手机号码验证成功", "phone": validated_phone}
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))


@router.get("/error-examples")
async def get_error_examples():
    """
    获取422错误示例
    """
    return {
        "examples": {
            "missing_field": {
                "description": "缺少必需字段",
                "request": "POST /validation/users/demo",
                "body": {"username": "test"},  # 缺少email和age
                "error": "缺少必需字段: email, age",
            },
            "type_error": {
                "description": "字段类型错误",
                "request": "POST /validation/users/demo",
                "body": {
                    "username": "test",
                    "email": "<EMAIL>",
                    "age": "not_a_number",
                },
                "error": "字段类型错误: age - value is not a valid integer",
            },
            "validation_error": {
                "description": "字段值验证失败",
                "request": "POST /validation/users/demo",
                "body": {"username": "test", "email": "<EMAIL>", "age": 15},
                "error": "字段值错误: age - ensure this value is greater than or equal to 18",
            },
            "enum_error": {
                "description": "枚举值错误",
                "request": "POST /validation/users/demo",
                "body": {
                    "username": "test",
                    "email": "<EMAIL>",
                    "age": 25,
                    "role": "invalid_role",
                },
                "error": "字段值错误: role - value is not a valid enumeration member",
            },
            "custom_validator_error": {
                "description": "自定义验证器失败",
                "request": "POST /validation/users/demo",
                "body": {"username": "test@#$", "email": "<EMAIL>", "age": 25},
                "error": "字段值错误: username - 用户名只能包含字母和数字",
            },
        }
    }
