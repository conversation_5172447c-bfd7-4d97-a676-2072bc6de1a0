from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.services.like_cache_service import like_cache_service

router = APIRouter()


@router.post("/toggle", response_model=schemas.LikeStatus)
async def toggle_like(
    *,
    db: AsyncSession = Depends(deps.get_db),
    like_data: schemas.LikeToggle,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """切换点赞状态"""
    # 验证内容是否存在
    if like_data.content_type == "article":
        content = await crud.article.get(db, id=like_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif like_data.content_type == "video":
        content = await crud.video.get(db, id=like_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 切换点赞状态
    like_obj, is_liked = await crud.like.toggle_like(
        db,
        user_id=current_user.id,
        content_type=like_data.content_type,
        content_id=like_data.content_id,
    )

    # 更新缓存
    if is_liked:
        like_cache_service.increment_like_count(like_data.content_type, like_data.content_id)
    else:
        like_cache_service.decrement_like_count(like_data.content_type, like_data.content_id)

    like_cache_service.set_user_like_status(
        current_user.id, like_data.content_type, like_data.content_id, is_liked
    )

    # 获取最新的点赞数量
    like_count = await crud.like.get_content_like_count(
        db, content_type=like_data.content_type, content_id=like_data.content_id
    )

    return schemas.LikeStatus(
        content_type=like_data.content_type,
        content_id=like_data.content_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.get("/status", response_model=schemas.LikeStatus)
async def get_like_status(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str = Query(..., description="内容类型"),
    content_id: int = Query(..., description="内容ID"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取点赞状态"""
    # 先尝试从缓存获取
    cached_count = like_cache_service.get_like_count(content_type, content_id)
    cached_status = like_cache_service.is_liked_by_user(current_user.id, content_type, content_id)

    if cached_count is not None and cached_status is not None:
        return schemas.LikeStatus(
            content_type=content_type,
            content_id=content_id,
            is_liked=cached_status,
            like_count=cached_count,
        )

    # 从数据库获取
    like_count = await crud.like.get_content_like_count(
        db, content_type=content_type, content_id=content_id
    )
    is_liked = await crud.like.is_liked_by_user(
        db, user_id=current_user.id, content_type=content_type, content_id=content_id
    )

    # 更新缓存
    like_cache_service.set_like_count(content_type, content_id, like_count)
    like_cache_service.set_user_like_status(current_user.id, content_type, content_id, is_liked)

    return schemas.LikeStatus(
        content_type=content_type,
        content_id=content_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.get("/history", response_model=schemas.LikeHistory)
async def get_like_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户点赞历史"""
    likes = await crud.like.get_user_likes(
        db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
    )
    total = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type=content_type
    )

    return schemas.LikeHistory(total=total, items=likes)


@router.get("/stats", response_model=schemas.LikeStats)
async def get_like_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取点赞统计信息（仅管理员）"""
    if not crud.user.is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问统计信息",
        )

    stats = await crud.like.get_like_stats(db, content_type=content_type)
    return schemas.LikeStats(**stats)


@router.post("/batch-status", response_model=list[schemas.ContentLikeInfo])
async def get_batch_like_status(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_items: list[schemas.LikeBase],
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """批量获取内容点赞信息"""
    if not content_items:
        return []

    # 转换为元组列表
    items = [(item.content_type, item.content_id) for item in content_items]

    # 先尝试从缓存批量获取
    cached_info = like_cache_service.batch_get_like_info(items, current_user.id)

    # 检查哪些需要从数据库获取
    missing_items = []
    for item_key in items:
        cache_data = cached_info.get(item_key, {})
        if cache_data.get("like_count") is None or cache_data.get("is_liked") is None:
            missing_items.append(item_key)

    # 从数据库获取缺失的数据
    if missing_items:
        db_info = await crud.like.get_content_likes_batch(
            db, content_items=missing_items, user_id=current_user.id
        )

        # 更新缓存
        for item_key, data in db_info.items():
            content_type, content_id = item_key
            like_cache_service.set_like_count(content_type, content_id, data["like_count"])
            like_cache_service.set_user_like_status(
                current_user.id, content_type, content_id, data["is_liked"]
            )

        # 合并数据
        cached_info.update(db_info)

    # 构建响应
    result = []
    for content_type, content_id in items:
        data = cached_info.get((content_type, content_id), {})
        result.append(
            schemas.ContentLikeInfo(
                content_type=content_type,
                content_id=content_id,
                like_count=data.get("like_count", 0),
                is_liked_by_user=data.get("is_liked", False),
            )
        )

    return result
