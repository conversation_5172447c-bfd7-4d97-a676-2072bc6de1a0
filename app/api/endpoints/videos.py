from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.Video)
async def create_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新视频"""
    video = await crud.video.create(db=db, obj_in=video_in)
    # 创建审核记录
    review_in = schemas.ReviewCreate(
        content_type="video",
        content_id=video.id,
    )
    await crud.review.create(db=db, obj_in=review_in)
    return video


@router.get("/", response_model=schemas.VideoList)
async def read_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频列表"""
    # 管理员可以看到所有视频，普通用户只能看到已发布的视频
    if crud.user.is_admin(current_user):
        videos = await crud.video.get_multi(db, skip=skip, limit=limit)
        result = await db.execute(select(models.Video))
        total = len(result.scalars().all())
    else:
        videos = await crud.video.get_published(db, skip=skip, limit=limit)
        result = await db.execute(select(models.Video).filter(models.Video.is_published))
        total = len(result.scalars().all())
    return {"total": total, "items": videos}


@router.get("/my", response_model=schemas.VideoList)
async def read_my_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的视频列表"""
    videos = await crud.video.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    result = await db.execute(
        select(models.Video).filter(models.Video.author_id == current_user.id)
    )
    total = len(result.scalars().all())
    return {"total": total, "items": videos}


@router.get("/{video_id}", response_model=schemas.Video)
async def read_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频详情"""
    video = await crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 非管理员且非作者只能查看已发布的视频
    if (
        not video.is_published
        and not crud.user.is_admin(current_user)
        and video.author_id != current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该视频",
        )
    return video


@router.put("/{video_id}", response_model=schemas.Video)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频"""
    video = await crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 只有管理员和作者可以更新视频
    if not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新该视频",
        )
    # 如果内容有更新，需要重新审核
    if video_in.title or video_in.description or video_in.url:
        video_in_dict = video_in.dict(exclude_unset=True)
        video_in_dict["is_published"] = False
        video = await crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
        # 创建新的审核记录
        review = await crud.review.get_by_content(db, content_type="video", content_id=video.id)
        if review:
            await crud.review.update(
                db=db,
                db_obj=review,
                obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None},
            )
        else:
            review_in = schemas.ReviewCreate(
                content_type="video",
                content_id=video.id,
            )
            await crud.review.create(db=db, obj_in=review_in)
    else:
        video = await crud.video.update(db=db, db_obj=video, obj_in=video_in)
    return video


@router.put("/{video_id}/move", response_model=schemas.Video)
async def move_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    folder_id: int | None = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """移动视频到指定文件夹"""
    video = await crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 只有管理员和作者可以移动视频
    if not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限移动该视频",
        )
    # 如果指定了文件夹，检查文件夹是否存在且属于当前用户
    if folder_id is not None:
        folder = await crud.video_folder.get(db, id=folder_id)
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件夹不存在",
            )
        if folder.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限使用该文件夹",
            )
    video = await crud.video.move_to_folder(db=db, db_obj=video, folder_id=folder_id)
    return video


@router.delete("/{video_id}", response_model=schemas.Video)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除视频"""
    video = await crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 只有管理员和作者可以删除视频
    if not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该视频",
        )
    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="video", content_id=video.id)
    if review:
        await crud.review.remove(db=db, id=review.id)
    video = await crud.video.remove(db=db, id=video_id)
    return video
