"""文件上传API接口"""

from __future__ import annotations

from enum import Enum

from fastapi import APIRouter, Depends, File, Query, Request, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.services.logger import get_logger
from app.services.partial_upload import PartialUpload

logger = get_logger(__name__)

router = APIRouter()


class ContentType(str, Enum):
    VIDEO = "video"  # 枚举成员名称实际是 VIDEO（全大写）
    IMAGE = "image"  # 枚举成员名称实际是 IMAGE（全大写）


@router.post("/", response_model=schemas.FileHash)
async def upload_file(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    file: UploadFile = File(...),
    current_user: models.User = Depends(deps.get_current_active_user),
    content_type: ContentType = Query(..., description="内容类型"),
):
    """文件上传接口"""
    redis_client = request.app.state.redis
    upload_client = PartialUpload()
    match content_type:
        case ContentType.IMAGE:
            file_hash = await upload_client.calculate_file_hash(file.file)
            # 检查Redis中是否存在
            exists = await redis_client.hexists("file_hashes", file_hash)
            logger.info(f"文件哈希值: {file_hash}, 是否存在: {exists}")
            if exists:
                file_path = await redis_client.hget("file_hashes", file_hash)
                return {"file_path": file_path, "file_hash": file_hash}
            # 查询数据库中是否存在
            db_hash = await crud.file_hash.get_by_hash(db, file_hash=file_hash)
            if db_hash:
                await redis_client.hset("file_hashes", file_hash, db_hash.file_path)
                return {"file_path": db_hash.file_path, "file_hash": file_hash}
            # 读取文件内容
            file_content = await file.read()

            # 调用Celery任务处理图片并上传到OSS
            from app.tasks.image_processing import process_and_upload_image

            task = await process_and_upload_image.delay(file_content, file_hash)
            result = await task.get()
            logger.info(f"上传结果: {result}")
            # if not oss_url:
            #     raise HTTPException(
            #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            #         detail="Failed to process and upload image",
            #     )

            # # 保存文件哈希记录
            # file_hash_obj = await crud.file_hash.create(
            #     db, obj_in=schemas.FileHashCreate(file_hash=file_hash, file_path=oss_url)
            # )

            # # 缓存文件路径
            # await redis_client.hset("file_hashes", file_hash, oss_url)

            # return {"file_path": oss_url, "file_hash": file_hash}
        case ContentType.VIDEO:
            # TODO: 视频上传
            pass
