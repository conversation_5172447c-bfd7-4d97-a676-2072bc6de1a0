"""用户行为追踪API接口"""

from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.services.recommendation_cache_service import recommendation_cache_service

router = APIRouter()


@router.post("/browse", response_model=schemas.UserBrowseHistory)
async def record_browse_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    browse_data: schemas.UserBrowseHistoryCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """记录用户浏览历史"""

    # 验证内容是否存在
    if browse_data.content_type == "article":
        query = select(models.Article).where(models.Article.id == browse_data.content_id)
        result = await db.execute(query)
        content = result.scalar_one_or_none()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif browse_data.content_type == "video":
        query = select(models.Video).where(models.Video.id == browse_data.content_id)
        result = await db.execute(query)
        content = result.scalar_one_or_none()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 创建浏览历史记录
    browse_history = await crud.user_browse_history.create_with_user(
        db=db, obj_in=browse_data, user_id=current_user.id
    )

    # 同时创建交互记录
    interaction_data = schemas.UserInteractionCreate(
        content_type=browse_data.content_type,
        content_id=browse_data.content_id,
        interaction_type="view",
        weight=1.0,
    )

    await crud.user_interaction.create_with_user(db=db, obj_in=interaction_data, user_id=current_user.id)

    # 清除用户推荐缓存（因为行为发生变化）
    recommendation_cache_service.invalidate_user_cache(current_user.id)

    return browse_history


@router.post("/interaction", response_model=schemas.UserInteraction)
async def record_user_interaction(
    *,
    db: AsyncSession = Depends(deps.get_db),
    interaction_data: schemas.UserInteractionCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """记录用户交互行为"""

    # 验证内容是否存在
    if interaction_data.content_type == "article":
        query = select(models.Article).where(models.Article.id == interaction_data.content_id)
        result = await db.execute(query)
        content = result.scalar_one_or_none()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif interaction_data.content_type == "video":
        query = select(models.Video).where(models.Video.id == interaction_data.content_id)
        result = await db.execute(query)
        content = result.scalar_one_or_none()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 验证交互类型
    valid_interaction_types = ["view", "like", "favorite", "comment", "share", "click"]
    if interaction_data.interaction_type not in valid_interaction_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的交互类型，支持的类型：{', '.join(valid_interaction_types)}",
        )

    # 创建交互记录
    interaction = await crud.user_interaction.create_with_user(
        db=db, obj_in=interaction_data, user_id=current_user.id
    )

    # 清除用户推荐缓存
    recommendation_cache_service.invalidate_user_cache(current_user.id)

    return interaction


@router.get("/browse-history", response_model=list[schemas.UserBrowseHistory])
async def get_browse_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    days: int = Query(30, ge=1, le=365, description="查询天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户浏览历史"""

    history = await crud.user_browse_history.get_user_history(
        db=db,
        user_id=current_user.id,
        content_type=content_type,
        skip=skip,
        limit=limit,
        days=days,
    )

    return history


@router.get("/interactions", response_model=list[schemas.UserInteraction])
async def get_user_interactions(
    *,
    db: AsyncSession = Depends(deps.get_db),
    interaction_type: str | None = Query(None, description="交互类型过滤"),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    days: int = Query(30, ge=1, le=365, description="查询天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户交互记录"""

    interactions = await crud.user_interaction.get_user_interactions(
        db=db,
        user_id=current_user.id,
        interaction_type=interaction_type,
        content_type=content_type,
        skip=skip,
        limit=limit,
        days=days,
    )

    return interactions


@router.get("/profile", response_model=schemas.UserProfile)
async def get_user_profile(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户画像"""

    profile = await crud.user_profile.get_by_user_id(db, user_id=current_user.id)

    if not profile:
        # 如果用户画像不存在，创建默认画像
        default_profile = schemas.UserProfileCreate()
        profile = await crud.user_profile.create_with_user(
            db=db, obj_in=default_profile, user_id=current_user.id
        )

    return profile


@router.put("/profile", response_model=schemas.UserProfile)
async def update_user_profile(
    *,
    db: AsyncSession = Depends(deps.get_db),
    profile_in: schemas.UserProfileUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新用户画像"""

    profile = await crud.user_profile.get_by_user_id(db, user_id=current_user.id)

    if not profile:
        raise HTTPException(
            status_code=404,
            detail="用户画像不存在",
        )

    profile = await crud.user_profile.update(
        db=db,
        db_obj=profile,
        obj_in=profile_in,
    )

    # 清除用户推荐缓存
    recommendation_cache_service.invalidate_user_cache(current_user.id)

    return profile


@router.get("/interest-analysis", response_model=schemas.UserInterestProfile)
async def get_user_interest_analysis(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(30, ge=7, le=365, description="分析天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户兴趣分析"""

    # 获取用户最近的交互记录
    interactions = crud.user_interaction.get_user_interactions(
        db=db,
        user_id=current_user.id,
        days=days,
        limit=1000,  # 获取更多数据用于分析
    )

    # 分析兴趣标签
    interest_tags = {}
    preferred_categories = {}
    total_weight = 0.0

    for interaction in interactions:
        total_weight += interaction.weight

        # 获取内容的标签和分类信息
        if interaction.content_type == "article":
            content = crud.article.get(db, id=interaction.content_id)
            if content:
                # 分析标签
                for tag in content.tags:
                    if tag.name not in interest_tags:
                        interest_tags[tag.name] = 0.0
                    interest_tags[tag.name] += interaction.weight

                # 分析分类
                if content.category:
                    if content.category.name not in preferred_categories:
                        preferred_categories[content.category.name] = 0.0
                    preferred_categories[content.category.name] += interaction.weight

        elif interaction.content_type == "video":
            content = crud.video.get(db, id=interaction.content_id)
            if content:
                # 分析标签
                for tag in content.tags:
                    if tag.name not in interest_tags:
                        interest_tags[tag.name] = 0.0
                    interest_tags[tag.name] += interaction.weight

                # 分析分类
                if content.category:
                    if content.category.name not in preferred_categories:
                        preferred_categories[content.category.name] = 0.0
                    preferred_categories[content.category.name] += interaction.weight

    # 归一化权重
    if total_weight > 0:
        for tag in interest_tags:
            interest_tags[tag] /= total_weight
        for category in preferred_categories:
            preferred_categories[category] /= total_weight

    # 计算活跃度分数
    activity_score = min(total_weight / 100.0, 1.0)  # 归一化到0-1

    # 获取用户画像中的内容偏好
    profile = crud.user_profile.get_by_user_id(db, user_id=current_user.id)
    content_preference = profile.content_preference if profile else "both"

    return schemas.UserInterestProfile(
        user_id=current_user.id,
        interest_tags=interest_tags,
        preferred_categories=preferred_categories,
        content_preference=content_preference,
        activity_score=activity_score,
        last_active=datetime.utcnow(),
    )


@router.get("/popular-content", response_model=list[dict])
async def get_popular_content(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    limit: int = Query(10, ge=1, le=50, description="返回的最大记录数"),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取热门浏览内容（基于浏览历史统计）"""

    popular_content = await crud.user_browse_history.get_popular_content(
        db=db,
        content_type=content_type,
        limit=limit,
        days=days,
    )

    # 转换为字典格式
    result = []
    for content_type_val, content_id, view_count, unique_users in popular_content:
        result.append(
            {
                "content_type": content_type_val,
                "content_id": content_id,
                "view_count": view_count,
                "unique_users": unique_users,
            }
        )

    return result
