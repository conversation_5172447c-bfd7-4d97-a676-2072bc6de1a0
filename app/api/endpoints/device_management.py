"""设备管理相关接口"""

from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.models.user import User
from app.models.user_device import UserDevice
from app.services.device_service import DeviceTrustService
from app.services.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()
device_service = DeviceTrustService()


class DeviceInfo(BaseModel):
    """设备信息响应模型"""

    id: int
    device_name: str | None = None
    device_type: str | None = None
    browser_name: str | None = None
    browser_version: str | None = None
    os_name: str | None = None
    os_version: str | None = None
    ip_address: str | None = None
    location: str | None = None
    is_trusted: bool
    trust_score: int
    login_count: int
    last_login_at: datetime
    first_login_at: datetime
    is_active: bool
    is_blocked: bool

    class Config:
        from_attributes = True


class DeviceListResponse(BaseModel):
    """设备列表响应模型"""

    devices: list[DeviceInfo]
    total: int
    trusted_count: int
    active_count: int


class DeviceActionRequest(BaseModel):
    """设备操作请求模型"""

    device_id: int = Field(..., description="设备ID")
    reason: str | None = Field(None, description="操作原因")


@router.get("/devices", response_model=DeviceListResponse)
async def get_user_devices(
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    include_blocked: bool = False,
) -> DeviceListResponse:
    """获取用户设备列表

    Args:
        current_user: 当前用户
        db: 数据库会话
        include_blocked: 是否包含被阻止的设备

    Returns:
        DeviceListResponse: 设备列表响应
    """
    try:
        # 构建查询
        query = select(UserDevice).where(UserDevice.user_id == current_user.id)

        if not include_blocked:
            query = query.where(UserDevice.is_blocked == False)

        # 按最后登录时间倒序排列
        query = query.order_by(UserDevice.last_login_at.desc())
        result = await db.execute(query)
        devices = result.scalars().all()

        # 统计信息
        total = len(devices)
        trusted_count = sum(1 for d in devices if d.is_trusted)
        active_count = sum(1 for d in devices if d.is_active and not d.is_blocked)

        return DeviceListResponse(
            devices=[DeviceInfo.model_validate(device) for device in devices],
            total=total,
            trusted_count=trusted_count,
            active_count=active_count,
        )

    except Exception as e:
        logger.error(f"获取用户设备列表失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取设备列表失败") from e


@router.get("/devices/{device_id}", response_model=DeviceInfo)
async def get_device_detail(
    device_id: int,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> DeviceInfo:
    """获取设备详情

    Args:
        device_id: 设备ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        DeviceInfo: 设备信息
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == device_id,
            UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        return DeviceInfo.model_validate(device)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备详情失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取设备详情失败") from e


@router.post("/devices/trust")
async def trust_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """信任设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id,
            UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        if device.is_blocked:
            raise HTTPException(status_code=400, detail="被阻止的设备无法设为信任")

        await device_service.trust_device(db, device)

        return {"message": "设备已设为信任"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"信任设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="信任设备失败") from e


@router.post("/devices/block")
async def block_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """阻止设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id,
            UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        reason = request.reason or "用户手动阻止"
        await device_service.block_device(db, device, reason)

        return {"message": "设备已被阻止"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"阻止设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="阻止设备失败") from e


@router.post("/devices/unblock")
async def unblock_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """解除阻止设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id,
            UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        if not device.is_blocked:
            raise HTTPException(status_code=400, detail="设备未被阻止")

        device.is_blocked = False
        device.is_active = True
        device.blocked_reason = None
        device.calculate_trust_score()

        await db.commit()

        return {"message": "设备阻止已解除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解除阻止设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="解除阻止设备失败") from e


@router.delete("/devices/{device_id}")
async def delete_device(
    device_id: int,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """删除设备记录

    Args:
        device_id: 设备ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == device_id,
            UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        await db.delete(device)
        await db.commit()

        return {"message": "设备记录已删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除设备记录失败：{str(e)}")
        raise HTTPException(status_code=500, detail="删除设备记录失败") from e
