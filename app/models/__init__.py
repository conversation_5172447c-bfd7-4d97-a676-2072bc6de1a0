# 使models目录成为一个Python包
from app.models.article import Article
from app.models.category import Category
from app.models.comment import Comment
from app.models.favorite import Favorite
from app.models.file_hash import FileHash
from app.models.like import Like
from app.models.review import Review
from app.models.tag import Tag
from app.models.user import Permission, User, UserRole
from app.models.user_behavior import (
    ContentSimilarity,
    RecommendationLog,
    UserBrowseHistory,
    UserInteraction,
    UserProfile,
)
from app.models.user_device import UserDevice
from app.models.video import Video
from app.models.video_folder import VideoFolder

# 在这里导入所有模型，以便在其他地方可以通过app.models导入
__all__ = [
    "Category",
    "Article",
    "Comment",
    "Favorite",
    "FileHash",
    "Like",
    "Review",
    "Tag",
    "User",
    "UserRole",
    "Video",
    "VideoFolder",
    "Permission",
    "UserBrowseHistory",
    "UserInteraction",
    "UserProfile",
    "RecommendationLog",
    "ContentSimilarity",
    "UserDevice",
]
