from datetime import datetime

from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship

from app.db.session import Base


class VideoFolder(Base):
    """视频文件夹数据模型"""

    __tablename__ = "video_folders"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    path = Column(String(255), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    user = relationship("User", back_populates="video_folders")
    videos = relationship("Video", back_populates="folder")

    def __repr__(self):
        return f"<VideoFolder {self.name}>"