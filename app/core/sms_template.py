"""阿里云短信服务模板配置"""

from enum import Enum


class SmsTemplate(Enum):
    """短信模板配置

    模板内容示例：
    登录验证码模板：您正在登录，验证码为：${code}，5分钟内有效，请勿泄露于他人！
    注册验证码模板：您正在注册账号，验证码为：${code}，5分钟内有效，请勿泄露于他人！
    """

    # 登录验证码模板
    LOGIN = {
        "template_code": "SMS_123456789",  # 请替换为您申请的模板CODE
        "sign_name": "您的签名",  # 请替换为您的签名
        "template_name": "登录验证码",
    }

    # 注册验证码模板
    REGISTER = {
        "template_code": "SMS_987654321",  # 请替换为您申请的模板CODE
        "sign_name": "您的签名",  # 请替换为您的签名
        "template_name": "注册验证码",
    }

    # 设备验证码模板
    DEVICE_VERIFICATION = {
        "template_code": "SMS_111222333",  # 请替换为您申请的模板CODE
        "sign_name": "您的签名",  # 请替换为您的签名
        "template_name": "设备验证码",
    }

    def __init__(self, template_info):
        self.template_code = template_info["template_code"]
        self.sign_name = template_info["sign_name"]
        self.template_name = template_info["template_name"]
