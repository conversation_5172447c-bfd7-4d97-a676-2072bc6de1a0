"""图片处理相关的Celery任务"""

from io import BytesIO

from PIL import Image

from app.core.celery import app
from app.core.config import get_settings
from app.services.logger import get_logger
from app.services.partial_upload import PartialUpload

logger = get_logger(__name__)
settings = get_settings()


@app.task(bind=True)
async def process_and_upload_image(
    self, file_data: bytes, file_hash: str, quality: int = 80
) -> str | None:
    """将图片转换为webp格式并上传到OSS

    Args:
        file_data: 图片二进制数据
        file_hash: 文件哈希值
        quality: webp质量，默认80

    Returns:
        str: OSS文件URL，如果处理或上传失败返回None
    """
    try:
        # 在内存中转换为webp
        with Image.open(BytesIO(file_data)) as img:
            output_buffer = BytesIO()
            img.save(output_buffer, "webp", quality=quality)
            webp_data = output_buffer.getvalue()
        partial_client = PartialUpload()
        oss_url = await partial_client.upload(webp_data, file_hash)
        return oss_url
    except Exception as e:
        logger.error(f"Failed to process and upload image: {e}")
        return None
