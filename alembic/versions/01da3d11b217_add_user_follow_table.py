"""add_user_follow_table

Revision ID: 01da3d11b217
Revises: 784100397d76
Create Date: 2025-06-23 09:26:06.269335

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "01da3d11b217"
down_revision: str | None = "784100397d76"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 创建用户关注关系表
    op.create_table(
        "user_follow",
        sa.Column("follower_id", sa.Integer(), nullable=False),
        sa.Column("followed_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False, server_default=sa.text("CURRENT_TIMESTAMP")),
        sa.ForeignKeyConstraint(["follower_id"], ["users.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["followed_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("follower_id", "followed_id")
    )
    # 创建索引以提高查询性能
    op.create_index(
        "ix_user_follow_follower_id",
        "user_follow",
        ["follower_id"],
        unique=False
    )
    op.create_index(
        "ix_user_follow_followed_id",
        "user_follow",
        ["followed_id"],
        unique=False
    )


def downgrade() -> None:
    # 删除索引
    op.drop_index("ix_user_follow_followed_id", table_name="user_follow")
    op.drop_index("ix_user_follow_follower_id", table_name="user_follow")
    # 删除表
    op.drop_table("user_follow")
