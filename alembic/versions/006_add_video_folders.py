"""add video folders

Revision ID: 006
Revises: 005_add_wechat_fields
Create Date: 2024-01-10 10:00:00.000000

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "006_add_video_folders"
down_revision = "005_add_wechat_fields"
branch_labels = None
depends_on = None


def upgrade():
    # 创建视频文件夹表
    op.create_table(
        "video_folders",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("path", sa.String(length=255), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ),
        sa.PrimaryKeyConstraint("id")
    )
    op.create_index(op.f("ix_video_folders_id"), "video_folders", ["id"], unique=False)
    op.create_index(op.f("ix_video_folders_path"), "video_folders", ["path"], unique=False)
    op.create_index(op.f("ix_video_folders_user_id"), "video_folders", ["user_id"], unique=False)

    # 添加视频文件夹外键
    op.add_column("videos", sa.Column("folder_id", sa.Integer(), nullable=True))
    op.create_index(op.f("ix_videos_folder_id"), "videos", ["folder_id"], unique=False)
    op.create_foreign_key(None, "videos", "video_folders", ["folder_id"], ["id"])


def downgrade():
    # 删除视频文件夹外键
    op.drop_constraint(None, "videos", type_="foreignkey")
    op.drop_index(op.f("ix_videos_folder_id"), table_name="videos")
    op.drop_column("videos", "folder_id")

    # 删除视频文件夹表
    op.drop_index(op.f("ix_video_folders_user_id"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_path"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_id"), table_name="video_folders")
    op.drop_table("video_folders")