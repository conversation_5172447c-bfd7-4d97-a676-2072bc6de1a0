"""empty message

Revision ID: 784100397d76
Revises: 003, 004
Create Date: 2025-06-23 09:25:53.170355

"""
from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "784100397d76"
down_revision: str | None = ("003", "004")
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
