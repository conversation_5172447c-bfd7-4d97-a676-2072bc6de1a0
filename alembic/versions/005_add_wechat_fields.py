"""Add WeChat fields to User model

Revision ID: 005
Revises: 20bae8059921
Create Date: 2025-06-24 02:50:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "005"
down_revision: str | None = "20bae8059921"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 修改users表的password字段为可空
    op.alter_column("users", "password", nullable=True)
    
    # 添加微信相关字段
    op.add_column("users", sa.Column("wechat_openid", sa.String(100), nullable=True, comment="微信OpenID"))
    op.add_column("users", sa.Column("wechat_unionid", sa.String(100), nullable=True, comment="微信UnionID"))
    op.add_column("users", sa.Column("wechat_nickname", sa.String(100), nullable=True, comment="微信昵称"))
    op.add_column("users", sa.Column("wechat_avatar", sa.String(255), nullable=True, comment="微信头像"))
    op.add_column("users", sa.Column("login_type", sa.String(20), nullable=False, server_default="password", comment="登录方式：password, wechat"))
    
    # 创建索引
    op.create_index("ix_users_wechat_openid", "users", ["wechat_openid"], unique=True)
    op.create_index("ix_users_wechat_unionid", "users", ["wechat_unionid"], unique=True)


def downgrade() -> None:
    # 删除索引
    op.drop_index("ix_users_wechat_unionid", "users")
    op.drop_index("ix_users_wechat_openid", "users")
    
    # 删除微信相关字段
    op.drop_column("users", "login_type")
    op.drop_column("users", "wechat_avatar")
    op.drop_column("users", "wechat_nickname")
    op.drop_column("users", "wechat_unionid")
    op.drop_column("users", "wechat_openid")
    
    # 恢复password字段为非空
    op.alter_column("users", "password", nullable=False)
